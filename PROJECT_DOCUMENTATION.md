# Optical Lines Database - Complete Project Documentation

## 📋 Project Overview

The Optical Lines Database is a comprehensive web application for managing and visualizing optical fiber network infrastructure. It provides an interactive interface for telecommunications providers to manage their optical lines, connection points, and network capacity with role-based access control and real-time visualization.

### 🎯 Project Goals
- **Network Management**: Centralized management of optical fiber infrastructure
- **Visualization**: Interactive map-based visualization of network topology
- **Provider Tools**: Dedicated dashboards for telecommunications providers
- **Data Management**: Comprehensive CRUD operations with advanced filtering
- **Capacity Monitoring**: Real-time capacity utilization tracking and alerts

## 🏗️ System Architecture

### Frontend Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Angular Frontend                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Auth     │  │    Map      │  │ Data Tables │         │
│  │   Module    │  │   Module    │  │   Module    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Provider   │  │   Shared    │  │    Core     │         │
│  │   Module    │  │ Components  │  │  Services   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                         HTTP/REST API
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Java Spring Boot Backend                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Controllers │  │  Services   │  │ Repositories│         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Security   │  │   Entities  │  │    DTOs     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                         JPA/Hibernate
                              │
┌─────────────────────────────────────────────────────────────┐
│                    PostgreSQL Database                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Users    │  │ Optical     │  │ Connection  │         │
│  │    Table    │  │   Lines     │  │   Points    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 💻 Technology Stack

### Frontend Technologies
- **Framework**: Angular 19.x
- **UI Library**: Angular Material 19.x
- **Map Visualization**: Leaflet 1.9.x
- **Styling**: CSS with Angular Material theming
- **State Management**: Angular services with RxJS
- **HTTP Client**: Angular HttpClient
- **Authentication**: JWT-based authentication
- **Build Tool**: Angular CLI with Webpack
- **Package Manager**: npm

### Backend Technologies
- **Framework**: Java 17 + Spring Boot 3.x
- **Security**: Spring Security with JWT
- **Database**: PostgreSQL (without PostGIS extensions)
- **ORM**: Spring Data JPA with Hibernate
- **Migration**: Flyway
- **Validation**: Bean Validation API
- **Documentation**: OpenAPI/Swagger
- **Build Tool**: Maven
- **Mapping**: MapStruct

### Development Tools
- **IDE**: Visual Studio Code / IntelliJ IDEA
- **Version Control**: Git
- **Database Tools**: pgAdmin / DBeaver
- **API Testing**: Postman / Swagger UI
- **Containerization**: Docker & Docker Compose

## 📊 Database Schema

### Core Entities

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'provider', 'viewer')),
    provider_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);
```

#### Connection Points Table
```sql
CREATE TABLE connection_points (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    provider_id UUID NOT NULL REFERENCES users(id),
    provider_name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('junction', 'endpoint', 'distribution')),
    capacity INTEGER NOT NULL,
    address TEXT,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'planned', 'maintenance', 'inactive')),
    installation_date DATE,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Optical Lines Table
```sql
CREATE TABLE optical_lines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    provider_id UUID NOT NULL REFERENCES users(id),
    provider_name VARCHAR(100) NOT NULL,
    start_point_id UUID NOT NULL REFERENCES connection_points(id),
    end_point_id UUID NOT NULL REFERENCES connection_points(id),
    capacity INTEGER NOT NULL,
    used_capacity INTEGER DEFAULT 0,
    length DECIMAL(10, 2) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'planned', 'maintenance', 'inactive')),
    installation_date DATE,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    geometry_coordinates TEXT NOT NULL,
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Data Relationships
- **Users** → **Connection Points** (1:N) - Provider owns multiple points
- **Users** → **Optical Lines** (1:N) - Provider owns multiple lines
- **Connection Points** → **Optical Lines** (1:N) - Point can have multiple connected lines
- **Optical Lines** → **Connection Points** (N:2) - Line connects exactly two points

## 🔐 Security & Authentication

### Role-Based Access Control (RBAC)
- **Admin**: Full system access, user management, all CRUD operations
- **Provider**: Manage own resources only, capacity monitoring, dashboard access
- **Viewer**: Read-only access to all public data

### Authentication Flow
1. User submits credentials to `/api/auth/login`
2. Backend validates credentials against database
3. JWT token generated and returned with user info
4. Frontend stores token and includes in Authorization header
5. Backend validates token on each protected request

### Security Features
- Password hashing with BCrypt
- JWT token expiration (24 hours)
- CORS configuration for frontend domain
- Input validation and sanitization
- SQL injection prevention via JPA
- XSS protection with proper encoding

## 🗺️ Geographic Data Handling

### Coordinate Storage Strategy
- **Simple Coordinates**: Latitude/longitude as DECIMAL columns (no PostGIS)
- **Line Geometry**: JSON string containing coordinate arrays
- **Format**: `[[lng1, lat1], [lng2, lat2], ...]`
- **GeoJSON Conversion**: Backend converts to/from GeoJSON for frontend

### Geographic Features
- **Point Visualization**: Markers on interactive map
- **Line Visualization**: Polylines connecting points
- **Bounding Box Queries**: Find entities within geographic bounds
- **Distance Calculations**: Haversine formula for line lengths
- **Provider Color Coding**: Different colors for different providers

## 📱 Frontend Features

### Implemented Features
- ✅ **Interactive Map**: Leaflet-based visualization with zoom/pan
- ✅ **Data Tables**: Sortable, filterable tables for lines and points
- ✅ **Authentication**: Login/register with role-based routing
- ✅ **Responsive Design**: Mobile-friendly Angular Material UI
- ✅ **Navigation**: Protected routes with role guards

### TODO Features (Currently Placeholders)
- 🚧 **Provider Dashboard**: Statistics, overview, recent activity
- 🚧 **Provider Line Management**: CRUD operations for provider's lines
- 🚧 **Provider Point Management**: CRUD operations for provider's points
- 🚧 **Capacity Management**: Utilization tracking and alerts
- 🚧 **Data Export**: CSV/JSON export functionality
- 🚧 **Search & Filtering**: Advanced search across entities
- 🚧 **Notifications**: Real-time alerts and status updates
- 🚧 **Analytics**: Usage trends and reporting

## 🔧 API Endpoints

### Authentication Endpoints
- `POST /api/auth/login` - User authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Current user info

### Core Data Endpoints
- `GET /api/lines` - Get optical lines (with optional provider filter)
- `GET /api/lines/{id}` - Get specific line
- `POST /api/lines` - Create new line
- `PUT /api/lines/{id}` - Update line
- `DELETE /api/lines/{id}` - Delete line

- `GET /api/points` - Get connection points (with optional provider filter)
- `GET /api/points/{id}` - Get specific point
- `POST /api/points` - Create new point
- `PUT /api/points/{id}` - Update point
- `DELETE /api/points/{id}` - Delete point

### Provider Dashboard Endpoints (TODO)
- `GET /api/provider/dashboard/stats` - Provider statistics
- `GET /api/provider/capacity/overview` - Capacity overview
- `GET /api/provider/lines` - Provider's lines only
- `GET /api/provider/points` - Provider's points only

## 📁 Project Structure

### Frontend Structure
```
src/
├── app/
│   ├── core/                    # Core functionality
│   │   ├── guards/             # Route guards
│   │   ├── interceptors/       # HTTP interceptors
│   │   ├── layout/             # Layout components
│   │   ├── models/             # TypeScript interfaces
│   │   └── services/           # Core services
│   ├── features/               # Feature modules
│   │   ├── auth/              # Authentication
│   │   ├── data-tables/       # Data management
│   │   ├── map/               # Map visualization
│   │   └── provider/          # Provider dashboard
│   ├── shared/                # Shared components
│   │   └── components/        # Reusable components
│   └── environments/          # Environment configs
└── assets/                    # Static assets
```

### Backend Structure (Recommended)
```
src/main/java/com/opticallines/
├── config/                    # Configuration classes
├── controller/               # REST controllers
├── dto/                     # Data Transfer Objects
├── entity/                  # JPA entities
├── repository/              # Data repositories
├── service/                 # Business logic
├── security/                # Security configuration
└── util/                    # Utility classes
```

## 🚀 Development Setup

### Prerequisites
- **Node.js**: v18+ with npm v9+
- **Java**: JDK 17+
- **PostgreSQL**: v15+
- **Git**: Latest version

### Frontend Setup
```bash
# Clone repository
git clone <repository-url>
cd optical-lines-database

# Install dependencies
npm install

# Start development server
npm start

# Access application
http://localhost:4200
```

### Backend Setup
```bash
# Create Spring Boot project
# Add dependencies: Web, Data JPA, Security, PostgreSQL, Flyway

# Configure database
spring.datasource.url=*************************************************
spring.datasource.username=optical_user
spring.datasource.password=optical_password

# Run application
mvn spring-boot:run

# Access API documentation
http://localhost:8080/swagger-ui.html
```

### Database Setup
```bash
# Start PostgreSQL with Docker
docker run --name postgres-optical \
  -e POSTGRES_DB=optical_lines_db \
  -e POSTGRES_USER=optical_user \
  -e POSTGRES_PASSWORD=optical_password \
  -p 5432:5432 -d postgres:15

# Run migrations
mvn flyway:migrate
```

## 📊 Sample Data

The system includes comprehensive sample data representing Slovak telecommunications infrastructure:

### Sample Users
- **Admin**: <EMAIL> / admin123
- **Provider**: <EMAIL> / provider123 (Slovak Telekom)
- **Provider2**: <EMAIL> / provider123 (Orange Slovakia)
- **Viewer**: <EMAIL> / viewer123

### Sample Network Data
- **12 Connection Points**: Across major Slovak cities (Bratislava, Košice, Žilina, etc.)
- **11 Optical Lines**: Connecting the points with realistic distances
- **2 Providers**: Slovak Telekom (8 lines) and Orange Slovakia (3 lines)
- **Various Statuses**: Active, planned, maintenance lines for testing

## 🧪 Testing Strategy

### Frontend Testing
- **Unit Tests**: Jasmine + Karma for components and services
- **Integration Tests**: Angular Testing Utilities
- **E2E Tests**: Cypress or Protractor
- **Mock Services**: Development-time API mocking

### Backend Testing
- **Unit Tests**: JUnit 5 + Mockito
- **Integration Tests**: @SpringBootTest with TestContainers
- **Repository Tests**: @DataJpaTest
- **Security Tests**: @WithMockUser annotations

## 🚀 Deployment

### Production Environment
- **Frontend**: Static hosting (Netlify, Vercel, or CDN)
- **Backend**: Container deployment (Docker + Kubernetes)
- **Database**: Managed PostgreSQL (AWS RDS, Google Cloud SQL)
- **Monitoring**: Application metrics and health checks

### Environment Configuration
```properties
# Production settings
spring.profiles.active=prod
spring.datasource.url=${DATABASE_URL}
app.jwt.secret=${JWT_SECRET}
app.cors.allowed-origins=${FRONTEND_URL}
```

## 📈 Future Enhancements

### Planned Features
- **Real-time Updates**: WebSocket integration for live data
- **Advanced Analytics**: Network performance metrics
- **Mobile App**: React Native or Flutter companion app
- **API Rate Limiting**: Request throttling and quotas
- **Audit Logging**: Comprehensive change tracking
- **Backup & Recovery**: Automated database backups

### Scalability Considerations
- **Caching**: Redis for session and data caching
- **Load Balancing**: Multiple backend instances
- **Database Optimization**: Indexing and query optimization
- **CDN Integration**: Static asset delivery optimization

## 🔄 Current Implementation Status

### ✅ Completed Features
- **Core Authentication**: Login/register with JWT tokens
- **Interactive Map**: Leaflet integration with line/point visualization
- **Data Tables**: Sortable tables for lines and connection points
- **Routing & Guards**: Protected routes with role-based access
- **Basic CRUD**: Read operations with mock data
- **Responsive UI**: Angular Material design system
- **Geographic Visualization**: GeoJSON rendering on map

### 🚧 In Progress / TODO
- **Provider Dashboard**: Statistics and overview (placeholder only)
- **Provider Management**: Line/point CRUD for providers (placeholder only)
- **Capacity Management**: Utilization tracking (placeholder only)
- **Real API Integration**: Currently using mock data
- **Search & Export**: Advanced filtering and data export
- **Notifications**: Alert system for capacity thresholds
- **Analytics**: Reporting and trend analysis

### 📊 Development Progress
- **Frontend**: ~60% complete (core features working, provider features TODO)
- **Backend**: ~0% complete (needs full implementation)
- **Database**: Schema designed, sample data prepared
- **Documentation**: Complete specifications available

## 🛠️ Development Workflow

### Git Workflow
```bash
# Feature development
git checkout -b feature/provider-dashboard
git commit -m "feat: implement provider dashboard statistics"
git push origin feature/provider-dashboard

# Create pull request for review
# Merge to main after approval
```

### Code Standards
- **Frontend**: Angular style guide, ESLint configuration
- **Backend**: Google Java style guide, Checkstyle
- **Database**: Snake_case naming, proper indexing
- **API**: RESTful conventions, OpenAPI documentation

### Quality Assurance
- **Code Reviews**: Required for all pull requests
- **Automated Testing**: CI/CD pipeline with test coverage
- **Performance Testing**: Load testing for API endpoints
- **Security Scanning**: Dependency vulnerability checks

## 📋 API Documentation

### Request/Response Format
```json
// Standard success response
{
  "data": { ... },
  "message": "Success",
  "timestamp": "2024-01-15T10:30:00Z"
}

// Standard error response
{
  "error": "Bad Request",
  "message": "Validation failed",
  "details": [
    {
      "field": "capacity",
      "message": "Capacity must be positive"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Authentication Headers
```http
Authorization: Bearer <jwt-token>
Content-Type: application/json
Accept: application/json
```

### Pagination Format
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "size": 20,
    "total": 150,
    "totalPages": 8
  }
}
```

## 🔧 Configuration Management

### Environment Variables
```bash
# Frontend (.env)
ANGULAR_API_URL=http://localhost:8080/api
ANGULAR_MAP_DEFAULT_CENTER=48.7,19.7
ANGULAR_MAP_DEFAULT_ZOOM=8

# Backend (application.properties)
DB_USERNAME=optical_user
DB_PASSWORD=optical_password
JWT_SECRET=your-secret-key
CORS_ALLOWED_ORIGINS=http://localhost:4200
```

### Feature Flags
```typescript
// Frontend feature flags
export const FEATURES = {
  REAL_TIME_UPDATES: false,
  ADVANCED_ANALYTICS: false,
  BULK_OPERATIONS: true,
  NOTIFICATION_SYSTEM: false
};
```

## 📊 Performance Metrics

### Target Performance
- **Page Load Time**: < 2 seconds
- **API Response Time**: < 200ms for simple queries
- **Map Rendering**: < 1 second for 1000+ points
- **Database Queries**: < 100ms for indexed queries

### Monitoring
- **Frontend**: Google Analytics, Core Web Vitals
- **Backend**: Micrometer metrics, Spring Boot Actuator
- **Database**: Query performance monitoring
- **Infrastructure**: Health checks and uptime monitoring

## 🔒 Security Considerations

### Data Protection
- **Encryption**: HTTPS for all communications
- **Password Security**: BCrypt hashing with salt
- **Token Security**: JWT with expiration and refresh
- **Input Validation**: Server-side validation for all inputs

### Access Control
- **Authentication**: Required for all protected routes
- **Authorization**: Role-based permissions
- **Data Isolation**: Providers can only access own data
- **Admin Controls**: Full system access for administrators

### Compliance
- **GDPR**: User data protection and privacy
- **Audit Trail**: All changes logged with user attribution
- **Data Retention**: Configurable retention policies
- **Backup Security**: Encrypted database backups

## 📚 Learning Resources

### Frontend Development
- [Angular Documentation](https://angular.dev/)
- [Angular Material Components](https://material.angular.io/)
- [Leaflet Documentation](https://leafletjs.com/reference.html)
- [RxJS Operators Guide](https://rxjs.dev/guide/operators)

### Backend Development
- [Spring Boot Documentation](https://spring.io/projects/spring-boot)
- [Spring Security Reference](https://docs.spring.io/spring-security/reference/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)

### DevOps & Deployment
- [Docker Documentation](https://docs.docker.com/)
- [PostgreSQL Docker Image](https://hub.docker.com/_/postgres)
- [Spring Boot Docker Guide](https://spring.io/guides/gs/spring-boot-docker/)

## 🤝 Contributing

### Getting Started
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Code Review Process
1. Automated tests must pass
2. Code coverage must be maintained
3. Security scan must pass
4. Peer review required
5. Documentation updated if needed

### Issue Reporting
- Use GitHub Issues for bug reports
- Include reproduction steps
- Provide environment details
- Add relevant labels and assignees

## 📞 Support & Contact

### Development Team
- **Frontend Lead**: [Contact Information]
- **Backend Lead**: [Contact Information]
- **DevOps Engineer**: [Contact Information]
- **Project Manager**: [Contact Information]

### Documentation
- **API Documentation**: Available at `/swagger-ui.html`
- **User Manual**: [Link to user documentation]
- **Developer Guide**: This document
- **Deployment Guide**: [Link to deployment docs]

---

This comprehensive documentation serves as the single source of truth for the Optical Lines Database project, covering all technical aspects, implementation details, and development guidelines. It should be updated regularly as the project evolves and new features are implemented.
