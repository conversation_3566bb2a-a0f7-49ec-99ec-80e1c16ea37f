import { Component, OnInit, AfterView<PERSON>nit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import * as L from 'leaflet';
import { Subscription, forkJoin } from 'rxjs';

import { MapService, GeoJsonService, LayerService, LayerInfo } from '../../services';
import { MapControlsComponent, MapLegendComponent } from '../../components';
import { OpticalLineService, ConnectionPointService } from '../../../../core/services';
import { OpticalLine, ConnectionPoint } from '../../../../core/models';
import { AlertComponent } from '../../../../shared/components/alert/alert.component';

@Component({
  selector: 'app-map-view',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MapControlsComponent,
    MapLegendComponent,
    AlertComponent
  ],
  template: `
    <div class="map-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Optical Network Map</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <app-alert *ngIf="error" [message]="error" type="error"></app-alert>

          <div class="map-wrapper">
            <div id="map" class="map"></div>

            <div *ngIf="loading" class="loading-overlay">
              <mat-spinner diameter="50"></mat-spinner>
            </div>

            <app-map-controls
              [layers]="layers"
              [activeBaseLayer]="activeBaseLayer"
              (zoomIn)="zoomIn()"
              (zoomOut)="zoomOut()"
              (resetView)="resetView()"
              (toggleLayer)="toggleLayer($event)"
              (changeBaseLayer)="changeBaseLayer($event)">
            </app-map-controls>

            <app-map-legend></app-map-legend>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .map-container {
      padding: 20px;
    }

    .map-wrapper {
      position: relative;
      height: 600px;
      border: 1px solid #ddd;
    }

    .map {
      height: 100%;
      width: 100%;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1001;
    }
  `
})
export class MapViewComponent implements OnInit, AfterViewInit, OnDestroy {
  loading = true;
  error = '';
  layers: LayerInfo[] = [];
  activeBaseLayer = 'osm';

  private subscriptions: Subscription[] = [];
  private lines: OpticalLine[] = [];
  private points: ConnectionPoint[] = [];

  constructor(
    private mapService: MapService,
    private layerService: LayerService,
    private geoJsonService: GeoJsonService,
    private lineService: OpticalLineService,
    private pointService: ConnectionPointService
  ) {}

  ngOnInit(): void {
    // Subscribe to layer changes
    this.subscriptions.push(
      this.layerService.layers$.subscribe(layers => {
        this.layers = layers;
      })
    );

    // Load data
    this.loadData();
  }

  ngAfterViewInit(): void {
    // Initialize the map
    setTimeout(() => {
      this.mapService.initMap('map');
    });
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load optical lines and connection points data
   */
  private loadData(): void {
    this.loading = true;

    forkJoin({
      lines: this.lineService.getLines(),
      points: this.pointService.getPoints()
    }).subscribe({
      next: (data) => {
        this.lines = data.lines;
        this.points = data.points;
        this.displayData();
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load map data. Please try again later.';
        this.loading = false;
        console.error('Error loading map data:', err);
      }
    });
  }

  /**
   * Display the loaded data on the map
   */
  private displayData(): void {
    // Subscribe to map ready event
    this.subscriptions.push(
      this.mapService.mapReady$.subscribe(ready => {
        if (ready) {
          // Display lines and points
          this.mapService.displayLines(this.lines);
          this.mapService.displayPoints(this.points);

          // Fit the map to show all data
          this.mapService.fitBounds();
        }
      })
    );
  }

  /**
   * Zoom in on the map
   */
  zoomIn(): void {
    const map = (this.mapService as any).map;
    if (map) {
      map.zoomIn();
    }
  }

  /**
   * Zoom out on the map
   */
  zoomOut(): void {
    const map = (this.mapService as any).map;
    if (map) {
      map.zoomOut();
    }
  }

  /**
   * Reset the map view to show all data
   */
  resetView(): void {
    this.mapService.fitBounds();
  }

  /**
   * Toggle a layer's visibility
   * @param layerId Layer ID to toggle
   */
  toggleLayer(layerId: string): void {
    this.layerService.toggleLayerVisibility(layerId);
  }

  /**
   * Change the base layer
   * @param layerId Base layer ID
   */
  changeBaseLayer(layerId: string): void {
    this.activeBaseLayer = layerId;
    this.layerService.setActiveBaseLayer(layerId);
  }
}
