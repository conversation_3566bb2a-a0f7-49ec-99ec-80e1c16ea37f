import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

import { LoadingSpinnerComponent } from '../../../../../shared/components/loading-spinner/loading-spinner.component';
import { AlertComponent } from '../../../../../shared/components/alert/alert.component';
import { ConfirmDialogComponent } from '../../../../../shared/components/confirm-dialog/confirm-dialog.component';
import { OpticalLineService } from '../../../../../core/services';
import { OpticalLine } from '../../../../../core/models';

@Component({
  selector: 'app-line-detail',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatDividerModule,
    RouterModule,
    MatDialogModule,
    LoadingSpinnerComponent,
    AlertComponent
  ],
  template: `
    <div class="line-detail-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Optical Line Details</mat-card-title>
          <mat-card-subtitle *ngIf="line">{{ line.name }}</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <app-alert *ngIf="error" [message]="error" type="error"></app-alert>

          <div *ngIf="loading" class="loading-container">
            <app-loading-spinner></app-loading-spinner>
          </div>

          <div *ngIf="line && !loading" class="line-details">
            <div class="actions-bar">
              <button mat-raised-button color="primary" (click)="onEdit()">
                <mat-icon>edit</mat-icon> Edit
              </button>
              <button mat-raised-button color="warn" (click)="onDelete()">
                <mat-icon>delete</mat-icon> Delete
              </button>
              <button mat-raised-button color="accent" (click)="onViewMap()">
                <mat-icon>map</mat-icon> View on Map
              </button>
            </div>

            <mat-divider class="divider"></mat-divider>

            <mat-tab-group>
              <mat-tab label="Basic Information">
                <div class="tab-content">
                  <div class="info-grid">
                    <div class="info-item">
                      <div class="info-label">ID</div>
                      <div class="info-value">{{ line.id }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Name</div>
                      <div class="info-value">{{ line.name }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Provider</div>
                      <div class="info-value">{{ line.providerName }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Status</div>
                      <div class="info-value">
                        <span class="status-chip" [ngClass]="'status-' + line.status">
                          {{ line.status }}
                        </span>
                      </div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Capacity</div>
                      <div class="info-value">{{ line.capacity }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Used Capacity</div>
                      <div class="info-value">{{ line.usedCapacity }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Length (km)</div>
                      <div class="info-value">{{ line.length }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Installation Date</div>
                      <div class="info-value">{{ line.installationDate | date }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Last Modified</div>
                      <div class="info-value">{{ line.lastModified | date }}</div>
                    </div>
                  </div>
                </div>
              </mat-tab>

              <mat-tab label="Connection Points">
                <div class="tab-content">
                  <div class="connection-points">
                    <div class="connection-point">
                      <h3>Start Point</h3>
                      <p><strong>ID:</strong> {{ line.startPointId }}</p>
                      <button mat-button color="primary" [routerLink]="['/points', line.startPointId]">
                        View Details
                      </button>
                    </div>

                    <div class="connection-point">
                      <h3>End Point</h3>
                      <p><strong>ID:</strong> {{ line.endPointId }}</p>
                      <button mat-button color="primary" [routerLink]="['/points', line.endPointId]">
                        View Details
                      </button>
                    </div>
                  </div>
                </div>
              </mat-tab>
            </mat-tab-group>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .line-detail-container {
      padding: 20px;
    }

    .loading-container {
      display: flex;
      justify-content: center;
      padding: 20px;
      min-height: 300px;
      align-items: center;
    }

    .actions-bar {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }

    .divider {
      margin-bottom: 20px;
    }

    .tab-content {
      padding: 20px 0;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }

    .info-item {
      display: flex;
      flex-direction: column;
    }

    .info-label {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.54);
      margin-bottom: 5px;
    }

    .info-value {
      font-size: 16px;
    }

    .connection-points {
      display: flex;
      gap: 30px;
      flex-wrap: wrap;
    }

    .connection-point {
      flex: 1;
      min-width: 250px;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
    }

    .connection-point h3 {
      margin-top: 0;
      margin-bottom: 10px;
    }

    .status-chip {
      padding: 4px 8px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      text-transform: capitalize;
    }

    .status-active {
      background-color: #e6f4ea;
      color: #137333;
    }

    .status-planned {
      background-color: #e8f0fe;
      color: #1a73e8;
    }

    .status-maintenance {
      background-color: #fef7e0;
      color: #b06000;
    }

    .status-inactive {
      background-color: #fce8e6;
      color: #c5221f;
    }
  `
})
export class LineDetailComponent implements OnInit {
  lineId: string = '';
  line?: OpticalLine;
  loading = true;
  error = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private lineService: OpticalLineService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.lineId = params['id'];
      this.loadLine();
    });
  }

  loadLine(): void {
    this.loading = true;
    this.error = '';

    this.lineService.getLine(this.lineId).subscribe({
      next: (line) => {
        this.line = line;
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load optical line details. Please try again later.';
        this.loading = false;
        console.error('Error loading line:', err);
      }
    });
  }

  onEdit(): void {
    console.log('Edit line:', this.line);
    // In a real implementation, we would navigate to a form or open a dialog
  }

  onDelete(): void {
    if (!this.line) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the line "${this.line.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Delete line:', this.line);
        // In a real implementation, we would call the service to delete the line
        // and then navigate back to the lines list
        this.router.navigate(['/lines']);
      }
    });
  }

  onViewMap(): void {
    // Navigate to the map view with this line highlighted
    this.router.navigate(['/map'], { queryParams: { highlight: this.lineId } });
  }


}
