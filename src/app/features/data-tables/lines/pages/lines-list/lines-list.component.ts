import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { RouterModule } from '@angular/router';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

import { DataTableComponent, TableColumn } from '../../../../../shared/components/data-table/data-table.component';
import { LoadingSpinnerComponent } from '../../../../../shared/components/loading-spinner/loading-spinner.component';
import { AlertComponent } from '../../../../../shared/components/alert/alert.component';
import { ConfirmDialogComponent } from '../../../../../shared/components/confirm-dialog/confirm-dialog.component';
import { OpticalLineService } from '../../../../../core/services';
import { OpticalLine } from '../../../../../core/models';

@Component({
  selector: 'app-lines-list',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    RouterModule,
    MatDialogModule,
    DataTableComponent,
    LoadingSpinnerComponent,
    AlertComponent
  ],
  template: `
    <div class="lines-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Optical Lines</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <app-alert *ngIf="error" [message]="error" type="error"></app-alert>

          <div *ngIf="loading" class="loading-container">
            <app-loading-spinner></app-loading-spinner>
          </div>

          <app-data-table
            *ngIf="!loading"
            [data]="lines"
            [columns]="columns"
            [showAddButton]="false"
            (edit)="onEdit($event)"
            (delete)="onDelete($event)"
            (view)="onView($event)"
            (export)="onExport()">
          </app-data-table>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .lines-container {
      padding: 20px;
    }

    .loading-container {
      display: flex;
      justify-content: center;
      padding: 20px;
    }
  `
})
export class LinesListComponent implements OnInit {
  lines: OpticalLine[] = [];
  loading = true;
  error = '';

  columns: TableColumn[] = [
    { name: 'Name', property: 'name', isModelProperty: true, visible: true, isLink: true, linkPrefix: '/lines', sortable: true, filter: true },
    { name: 'Provider', property: 'providerName', isModelProperty: true, visible: true, sortable: true, filter: true },
    { name: 'Capacity', property: 'capacity', isModelProperty: true, visible: true, sortable: true, filter: true },
    { name: 'Used Capacity', property: 'usedCapacity', isModelProperty: true, visible: true, sortable: true, filter: true },
    { name: 'Length (km)', property: 'length', isModelProperty: true, visible: true, sortable: true, filter: true },
    { name: 'Status', property: 'status', isModelProperty: true, visible: true, sortable: true, filter: true },
    {
      name: 'Installation Date',
      property: 'installationDate',
      isModelProperty: true,
      visible: true,
      sortable: true,
      filter: true,
      format: (value: Date) => value ? new Date(value).toLocaleDateString() : ''
    }
  ];

  constructor(
    private lineService: OpticalLineService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadLines();
  }

  loadLines(): void {
    this.loading = true;
    this.error = '';

    this.lineService.getLines().subscribe({
      next: (lines) => {
        this.lines = lines;
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load optical lines. Please try again later.';
        this.loading = false;
        console.error('Error loading lines:', err);
      }
    });
  }

  onAdd(): void {
    // Navigate to add form or open dialog
    console.log('Add new line');
    // In a real implementation, we would navigate to a form or open a dialog
  }

  onEdit(line: OpticalLine): void {
    console.log('Edit line:', line);
    // In a real implementation, we would navigate to a form or open a dialog
  }

  onDelete(line: OpticalLine): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the line "${line.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Delete line:', line);
        // In a real implementation, we would call the service to delete the line
      }
    });
  }

  onView(line: OpticalLine): void {
    console.log('View line:', line);
    // In a real implementation, we would navigate to the detail view
  }

  onExport(): void {
    console.log('Export lines');
    // In a real implementation, we would export the data to CSV or PDF
  }


}
