import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { RouterModule } from '@angular/router';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

import { DataTableComponent, TableColumn } from '../../../../../shared/components/data-table/data-table.component';
import { LoadingSpinnerComponent } from '../../../../../shared/components/loading-spinner/loading-spinner.component';
import { AlertComponent } from '../../../../../shared/components/alert/alert.component';
import { ConfirmDialogComponent } from '../../../../../shared/components/confirm-dialog/confirm-dialog.component';
import { ConnectionPointService } from '../../../../../core/services';
import { ConnectionPoint } from '../../../../../core/models';

@Component({
  selector: 'app-points-list',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    RouterModule,
    MatDialogModule,
    DataTableComponent,
    LoadingSpinnerComponent,
    AlertComponent
  ],
  template: `
    <div class="points-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Connection Points</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <app-alert *ngIf="error" [message]="error" type="error"></app-alert>

          <div *ngIf="loading" class="loading-container">
            <app-loading-spinner></app-loading-spinner>
          </div>

          <app-data-table
            *ngIf="!loading"
            [data]="points"
            [columns]="columns"
            [showAddButton]="false"
            (edit)="onEdit($event)"
            (delete)="onDelete($event)"
            (view)="onView($event)"
            (export)="onExport()">
          </app-data-table>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .points-container {
      padding: 20px;
    }

    .loading-container {
      display: flex;
      justify-content: center;
      padding: 20px;
    }
  `
})
export class PointsListComponent implements OnInit {
  points: ConnectionPoint[] = [];
  loading = true;
  error = '';

  columns: TableColumn[] = [
    { name: 'Name', property: 'name', isModelProperty: true, visible: true, isLink: true, linkPrefix: '/points', sortable: true, filter: true },
    { name: 'Provider', property: 'providerName', isModelProperty: true, visible: true, sortable: true, filter: true },
    { name: 'Type', property: 'type', isModelProperty: true, visible: true, sortable: true, filter: true },
    { name: 'Capacity', property: 'capacity', isModelProperty: true, visible: true, sortable: true, filter: true },
    { name: 'Address', property: 'address', isModelProperty: true, visible: true, sortable: true, filter: true },
    { name: 'Status', property: 'status', isModelProperty: true, visible: true, sortable: true, filter: true },
    {
      name: 'Installation Date',
      property: 'installationDate',
      isModelProperty: true,
      visible: true,
      sortable: true,
      filter: true,
      format: (value: Date) => value ? new Date(value).toLocaleDateString() : ''
    }
  ];

  constructor(
    private pointService: ConnectionPointService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadPoints();
  }

  loadPoints(): void {
    this.loading = true;
    this.error = '';

    this.pointService.getPoints().subscribe({
      next: (points) => {
        this.points = points;
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load connection points. Please try again later.';
        this.loading = false;
        console.error('Error loading points:', err);
      }
    });
  }

  onAdd(): void {
    // Navigate to add form or open dialog
    console.log('Add new connection point');
    // In a real implementation, we would navigate to a form or open a dialog
  }

  onEdit(point: ConnectionPoint): void {
    console.log('Edit connection point:', point);
    // In a real implementation, we would navigate to a form or open a dialog
  }

  onDelete(point: ConnectionPoint): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the connection point "${point.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Delete connection point:', point);
        // In a real implementation, we would call the service to delete the point
      }
    });
  }

  onView(point: ConnectionPoint): void {
    console.log('View connection point:', point);
    // In a real implementation, we would navigate to the detail view
  }

  onExport(): void {
    console.log('Export connection points');
    // In a real implementation, we would export the data to CSV or PDF
  }


}
