import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

import { LoadingSpinnerComponent } from '../../../../../shared/components/loading-spinner/loading-spinner.component';
import { AlertComponent } from '../../../../../shared/components/alert/alert.component';
import { ConfirmDialogComponent } from '../../../../../shared/components/confirm-dialog/confirm-dialog.component';
import { ConnectionPointService } from '../../../../../core/services';
import { ConnectionPoint } from '../../../../../core/models';

@Component({
  selector: 'app-point-detail',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatDividerModule,
    MatChipsModule,
    RouterModule,
    MatDialogModule,
    LoadingSpinnerComponent,
    AlertComponent
  ],
  template: `
    <div class="point-detail-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Connection Point Details</mat-card-title>
          <mat-card-subtitle *ngIf="point">{{ point.name }}</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <app-alert *ngIf="error" [message]="error" type="error"></app-alert>

          <div *ngIf="loading" class="loading-container">
            <app-loading-spinner></app-loading-spinner>
          </div>

          <div *ngIf="point && !loading" class="point-details">
            <div class="actions-bar">
              <button mat-raised-button color="primary" (click)="onEdit()">
                <mat-icon>edit</mat-icon> Edit
              </button>
              <button mat-raised-button color="warn" (click)="onDelete()">
                <mat-icon>delete</mat-icon> Delete
              </button>
              <button mat-raised-button color="accent" (click)="onViewMap()">
                <mat-icon>map</mat-icon> View on Map
              </button>
            </div>

            <mat-divider class="divider"></mat-divider>

            <mat-tab-group>
              <mat-tab label="Basic Information">
                <div class="tab-content">
                  <div class="info-grid">
                    <div class="info-item">
                      <div class="info-label">ID</div>
                      <div class="info-value">{{ point.id }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Name</div>
                      <div class="info-value">{{ point.name }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Provider</div>
                      <div class="info-value">{{ point.providerName }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Type</div>
                      <div class="info-value">{{ point.type }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Status</div>
                      <div class="info-value">
                        <span class="status-chip" [ngClass]="'status-' + point.status">
                          {{ point.status }}
                        </span>
                      </div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Capacity</div>
                      <div class="info-value">{{ point.capacity }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Address</div>
                      <div class="info-value">{{ point.address }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Installation Date</div>
                      <div class="info-value">{{ point.installationDate | date }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Last Modified</div>
                      <div class="info-value">{{ point.lastModified | date }}</div>
                    </div>
                  </div>
                </div>
              </mat-tab>

              <mat-tab label="Connected Lines">
                <div class="tab-content">
                  <div *ngIf="point.connectedLines && point.connectedLines.length > 0" class="connected-lines">
                    <h3>Connected Lines</h3>
                    <div class="lines-list">
                      <div *ngFor="let lineId of point.connectedLines" class="line-item">
                        <mat-chip color="primary" [routerLink]="['/lines', lineId]">
                          Line ID: {{ lineId }}
                        </mat-chip>
                      </div>
                    </div>
                  </div>

                  <div *ngIf="!point.connectedLines || point.connectedLines.length === 0" class="no-lines">
                    <p>No connected lines found for this connection point.</p>
                  </div>
                </div>
              </mat-tab>
            </mat-tab-group>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .point-detail-container {
      padding: 20px;
    }

    .loading-container {
      display: flex;
      justify-content: center;
      padding: 20px;
      min-height: 300px;
      align-items: center;
    }

    .actions-bar {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }

    .divider {
      margin-bottom: 20px;
    }

    .tab-content {
      padding: 20px 0;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }

    .info-item {
      display: flex;
      flex-direction: column;
    }

    .info-label {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.54);
      margin-bottom: 5px;
    }

    .info-value {
      font-size: 16px;
    }

    .connected-lines {
      margin-top: 10px;
    }

    .lines-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
    }

    .line-item {
      cursor: pointer;
    }

    .no-lines {
      color: rgba(0, 0, 0, 0.54);
      font-style: italic;
    }

    .status-chip {
      padding: 4px 8px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
      text-transform: capitalize;
    }

    .status-active {
      background-color: #e6f4ea;
      color: #137333;
    }

    .status-planned {
      background-color: #e8f0fe;
      color: #1a73e8;
    }

    .status-maintenance {
      background-color: #fef7e0;
      color: #b06000;
    }

    .status-inactive {
      background-color: #fce8e6;
      color: #c5221f;
    }
  `
})
export class PointDetailComponent implements OnInit {
  pointId: string = '';
  point?: ConnectionPoint;
  loading = true;
  error = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private pointService: ConnectionPointService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.pointId = params['id'];
      this.loadPoint();
    });
  }

  loadPoint(): void {
    this.loading = true;
    this.error = '';

    this.pointService.getPoint(this.pointId).subscribe({
      next: (point) => {
        this.point = point;
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load connection point details. Please try again later.';
        this.loading = false;
        console.error('Error loading point:', err);
      }
    });
  }

  onEdit(): void {
    console.log('Edit connection point:', this.point);
    // In a real implementation, we would navigate to a form or open a dialog
  }

  onDelete(): void {
    if (!this.point) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '350px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the connection point "${this.point.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Delete connection point:', this.point);
        // In a real implementation, we would call the service to delete the point
        // and then navigate back to the points list
        this.router.navigate(['/points']);
      }
    });
  }

  onViewMap(): void {
    // Navigate to the map view with this point highlighted
    this.router.navigate(['/map'], { queryParams: { highlight: this.pointId } });
  }


}
